import React from 'react'
import { cn } from '../../lib/utils'

interface LinkProps extends React.AnchorHTMLAttributes<HTMLAnchorElement> {
  href: string
  children: React.ReactNode
  prefetch?: boolean
}

const Link = React.forwardRef<HTMLAnchorElement, LinkProps>(
  ({ className, href, children, prefetch, ...props }, ref) => {
    return (
      <a
        className={cn(className)}
        href={href}
        ref={ref}
        {...props}
      >
        {children}
      </a>
    )
  }
)

Link.displayName = 'Link'

export { Link }
