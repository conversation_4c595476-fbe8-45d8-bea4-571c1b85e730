import React from 'react'
import { <PERSON>, <PERSON>R<PERSON>, <PERSON>u } from 'lucide-react'
import { But<PERSON> } from './ui/Button'
import { Link } from './ui/Link'

const Header: React.FC = () => {
  return (
    <header className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-5xl px-4">
      <div className="bg-white rounded-full shadow-xl border border-gray-100 px-6 py-3 flex items-center justify-between">
        <Link href="#" className="flex items-center justify-center" prefetch={false}>
          <Heart className="h-6 w-6 text-primary-600" />
          <span className="ml-2 text-xl font-bold text-gray-900">FurSure</span>
        </Link>
        <nav className="hidden lg:flex gap-6 items-center">
          <Link
            href="#pricing"
            className="text-sm font-medium hover:text-primary-600 transition-colors text-gray-600"
            prefetch={false}
          >
            Pricing
          </Link>
          <Link
            href="#faq"
            className="text-sm font-medium hover:text-primary-600 transition-colors text-gray-600"
            prefetch={false}
          >
            FAQs
          </Link>
          <Link
            href="#"
            className="text-sm font-medium hover:text-primary-600 transition-colors text-gray-600"
            prefetch={false}
          >
            Blog
          </Link>
        </nav>
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            className="text-sm bg-primary-50 text-primary-700 hover:bg-primary-100 rounded-full px-4 py-2 font-medium border border-primary-200"
          >
            Sign In
          </Button>
          <Button className="text-sm bg-primary-600 text-white hover:bg-primary-700 rounded-full px-4 py-2 font-medium shadow-lg">
            Get Started for Free <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
        <Button variant="ghost" className="lg:hidden">
          <Menu className="h-6 w-6" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </div>
    </header>
  )
}

export default Header
